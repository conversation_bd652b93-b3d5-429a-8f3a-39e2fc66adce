using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace MotoKingPro.MediaTek
{
    /// <summary>
    /// Manager for MediaTek payloads, preloaders, and DA files
    /// </summary>
    public class PayloadManager
    {
        #region Constants
        private const string PAYLOADS_PATH = "bin/payloads";
        private const string PRELOADERS_PATH = "bin/Preloader";
        private const string DA_PATH = "bin/DA";
        private const string LOADER_PATH = "bin/loader";
        #endregion

        #region Events
        public event EventHandler<string> LogMessage;
        #endregion

        #region Payload Management
        public List<PayloadInfo> GetAvailablePayloads()
        {
            var payloads = new List<PayloadInfo>();

            try
            {
                if (Directory.Exists(PAYLOADS_PATH))
                {
                    var payloadFiles = Directory.GetFiles(PAYLOADS_PATH, "*.bin");
                    
                    foreach (var file in payloadFiles)
                    {
                        var payload = new PayloadInfo
                        {
                            Name = Path.GetFileNameWithoutExtension(file),
                            FilePath = file,
                            FileSize = new FileInfo(file).Length,
                            ChipsetType = ExtractChipsetFromFilename(file),
                            PayloadType = DeterminePayloadType(file)
                        };
                        
                        payloads.Add(payload);
                    }
                }
                
                LogMessage?.Invoke(this, $"Found {payloads.Count} payload files");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error loading payloads: {ex.Message}");
            }

            return payloads.OrderBy(p => p.ChipsetType).ToList();
        }

        public List<PreloaderInfo> GetAvailablePreloaders()
        {
            var preloaders = new List<PreloaderInfo>();

            try
            {
                if (Directory.Exists(PRELOADERS_PATH))
                {
                    var preloaderFiles = Directory.GetFiles(PRELOADERS_PATH, "*.bin");
                    
                    foreach (var file in preloaderFiles)
                    {
                        var preloader = new PreloaderInfo
                        {
                            Name = Path.GetFileNameWithoutExtension(file),
                            FilePath = file,
                            FileSize = new FileInfo(file).Length,
                            DeviceModel = ExtractDeviceModelFromFilename(file),
                            ChipsetType = ExtractChipsetFromPreloaderFilename(file),
                            IsPatched = file.Contains("patched")
                        };
                        
                        preloaders.Add(preloader);
                    }
                }
                
                LogMessage?.Invoke(this, $"Found {preloaders.Count} preloader files");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error loading preloaders: {ex.Message}");
            }

            return preloaders.OrderBy(p => p.DeviceModel).ToList();
        }

        public PayloadInfo GetPayloadForChipset(string chipset)
        {
            var payloads = GetAvailablePayloads();
            return payloads.FirstOrDefault(p => p.ChipsetType.Equals(chipset, StringComparison.OrdinalIgnoreCase));
        }

        public PreloaderInfo GetPreloaderForDevice(string deviceModel)
        {
            var preloaders = GetAvailablePreloaders();
            return preloaders.FirstOrDefault(p =>
                p.DeviceModel.IndexOf(deviceModel, StringComparison.OrdinalIgnoreCase) >= 0 ||
                p.Name.IndexOf(deviceModel, StringComparison.OrdinalIgnoreCase) >= 0);
        }
        #endregion

        #region DA (Download Agent) Management
        public List<DAInfo> GetAvailableDownloadAgents()
        {
            var daFiles = new List<DAInfo>();

            try
            {
                if (Directory.Exists(DA_PATH))
                {
                    var universalPath = Path.Combine(DA_PATH, "universal");
                    if (Directory.Exists(universalPath))
                    {
                        var files = Directory.GetFiles(universalPath, "*.*");
                        
                        foreach (var file in files)
                        {
                            var da = new DAInfo
                            {
                                Name = Path.GetFileName(file),
                                FilePath = file,
                                FileSize = new FileInfo(file).Length,
                                IsUniversal = true,
                                Version = ExtractVersionFromDAFilename(file)
                            };
                            
                            daFiles.Add(da);
                        }
                    }
                }
                
                LogMessage?.Invoke(this, $"Found {daFiles.Count} DA files");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error loading DA files: {ex.Message}");
            }

            return daFiles;
        }
        #endregion

        #region Loader Management
        public List<LoaderInfo> GetAvailableLoaders()
        {
            var loaders = new List<LoaderInfo>();

            try
            {
                if (Directory.Exists(LOADER_PATH))
                {
                    var autoPath = Path.Combine(LOADER_PATH, "auto");
                    if (Directory.Exists(autoPath))
                    {
                        var files = Directory.GetFiles(autoPath, "*.*");
                        
                        foreach (var file in files)
                        {
                            var loader = new LoaderInfo
                            {
                                Name = Path.GetFileName(file),
                                FilePath = file,
                                FileSize = new FileInfo(file).Length,
                                LoaderType = DetermineLoaderType(file)
                            };
                            
                            loaders.Add(loader);
                        }
                    }
                    
                    // Check for data.ext file
                    var dataExtPath = Path.Combine(LOADER_PATH, "data.ext");
                    if (File.Exists(dataExtPath))
                    {
                        var dataExt = new LoaderInfo
                        {
                            Name = "data.ext",
                            FilePath = dataExtPath,
                            FileSize = new FileInfo(dataExtPath).Length,
                            LoaderType = "Extension Data"
                        };
                        
                        loaders.Add(dataExt);
                    }
                }
                
                LogMessage?.Invoke(this, $"Found {loaders.Count} loader files");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error loading loaders: {ex.Message}");
            }

            return loaders;
        }
        #endregion

        #region Helper Methods
        private string ExtractChipsetFromFilename(string filename)
        {
            var name = Path.GetFileNameWithoutExtension(filename);
            
            // Extract MT#### pattern
            var match = System.Text.RegularExpressions.Regex.Match(name, @"mt(\d{4})");
            if (match.Success)
            {
                return $"MT{match.Groups[1].Value}";
            }
            
            return "Unknown";
        }

        private string ExtractChipsetFromPreloaderFilename(string filename)
        {
            var name = Path.GetFileNameWithoutExtension(filename);
            
            // Look for various chipset patterns
            var patterns = new[]
            {
                @"(\d{4})",  // 4-digit numbers like 6765, 6771
                @"mt(\d{4})", // MT prefix
                @"k(\d{2})v", // k##v pattern
                @"(\d{4})_", // 4-digit with underscore
            };
            
            foreach (var pattern in patterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(name, pattern);
                if (match.Success)
                {
                    var chipset = match.Groups[1].Value;
                    if (chipset.Length == 4)
                    {
                        return $"MT{chipset}";
                    }
                    else if (chipset.Length == 2)
                    {
                        return $"MT67{chipset}"; // Assume 67xx series for k##v pattern
                    }
                }
            }
            
            return "Unknown";
        }

        private string ExtractDeviceModelFromFilename(string filename)
        {
            var name = Path.GetFileNameWithoutExtension(filename);
            
            // Remove common prefixes
            name = name.Replace("preloader_", "");
            
            // Extract device model patterns
            var devicePatterns = new[]
            {
                @"([A-Z]\d+[A-Z]?)", // A32F, M013F, etc.
                @"(oppo\w+)", // OPPO devices
                @"(realme\w+)", // Realme devices
                @"(samsung\w+)", // Samsung devices
                @"(xiaomi\w+)", // Xiaomi devices
                @"([A-Z][a-z]+_[A-Z]\w+)", // Brand_Model pattern
            };
            
            foreach (var pattern in devicePatterns)
            {
                var match = System.Text.RegularExpressions.Regex.Match(name, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }
            
            // Fallback: take first part before underscore
            var parts = name.Split('_');
            return parts.Length > 0 ? parts[0] : "Unknown";
        }

        private string DeterminePayloadType(string filename)
        {
            var name = Path.GetFileNameWithoutExtension(filename).ToLower();
            
            if (name.Contains("dump")) return "Dump Payload";
            if (name.Contains("loader")) return "Loader Payload";
            if (name.Contains("patcher")) return "Patcher Payload";
            if (name.Contains("reboot")) return "Reboot Payload";
            if (name.Contains("uart")) return "UART Payload";
            if (name.Contains("sram")) return "SRAM Payload";
            if (name.Contains("stage")) return "Stage Payload";
            if (name.Contains("da_")) return "Download Agent";
            if (name.Contains("generic")) return "Generic Payload";
            
            return "Unknown Payload";
        }

        private string ExtractVersionFromDAFilename(string filename)
        {
            var name = Path.GetFileNameWithoutExtension(filename);
            var match = System.Text.RegularExpressions.Regex.Match(name, @"v(\d+\.?\d*)");
            return match.Success ? match.Groups[1].Value : "Unknown";
        }

        private string DetermineLoaderType(string filename)
        {
            var ext = Path.GetExtension(filename).ToLower();
            var name = Path.GetFileNameWithoutExtension(filename).ToLower();
            
            if (ext == ".bin") return "Binary Loader";
            if (ext == ".dll") return "Library Loader";
            if (ext == ".exe") return "Executable Loader";
            if (name.Contains("auto")) return "Auto Loader";
            
            return "Unknown Loader";
        }
        #endregion
    }

    #region Data Classes
    public class PayloadInfo
    {
        public string Name { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ChipsetType { get; set; }
        public string PayloadType { get; set; }
        
        public string SizeString => FormatBytes(FileSize);
        
        public override string ToString()
        {
            return $"{ChipsetType} - {PayloadType} ({SizeString})";
        }
        
        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    public class PreloaderInfo
    {
        public string Name { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string DeviceModel { get; set; }
        public string ChipsetType { get; set; }
        public bool IsPatched { get; set; }
        
        public string SizeString => FormatBytes(FileSize);
        
        public override string ToString()
        {
            var patchedStr = IsPatched ? " (Patched)" : "";
            return $"{DeviceModel} - {ChipsetType}{patchedStr}";
        }
        
        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    public class DAInfo
    {
        public string Name { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public bool IsUniversal { get; set; }
        public string Version { get; set; }
        
        public override string ToString()
        {
            var universalStr = IsUniversal ? " (Universal)" : "";
            return $"{Name} v{Version}{universalStr}";
        }
    }

    public class LoaderInfo
    {
        public string Name { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string LoaderType { get; set; }
        
        public override string ToString()
        {
            return $"{Name} - {LoaderType}";
        }
    }
    #endregion
}
